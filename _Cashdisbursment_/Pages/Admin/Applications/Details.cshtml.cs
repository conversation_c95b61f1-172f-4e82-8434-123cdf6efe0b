using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin.Applications
{
    public class DetailsModel : PageModel
    {
        private readonly ApplicationService _applicationService;
        private readonly ApprovalService _approvalService;

        public DetailsModel(ApplicationService applicationService, ApprovalService approvalService)
        {
            _applicationService = applicationService;
            _approvalService = approvalService;
        }

        public User? CurrentUser { get; set; }
        public Application? Application { get; set; }
        public int MaxApprovalLevel { get; set; }

        public async Task<IActionResult> OnGetAsync(int id)
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsAdminOrApprover(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            Application = await _applicationService.GetApplicationByIdAsync(id);
            
            if (Application == null)
            {
                return NotFound();
            }

            MaxApprovalLevel = await _approvalService.GetMaxApprovalLevelAsync();

            return Page();
        }
    }
}
