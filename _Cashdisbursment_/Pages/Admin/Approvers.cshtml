@page
@model _Cashdisbursment_.Pages.Admin.ApproversModel
@{
    ViewData["Title"] = "Manage Approvers";
}

<div class="container">
    <div class="page-header p-5">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="h3 mb-2">Manage Approvers</h1>
                <p class="text-muted mb-0">Configure the multi-level approval workflow</p>
            </div>
            <div class="col-auto">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addApproverModal">
                    <i class="fas fa-plus me-2"></i>Add Approver
                </button>
            </div>
        </div>
        
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="py-section">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Approval Hierarchy</h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Approvers.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Level</th>
                                            <th>Email</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var approver in Model.Approvers.OrderBy(a => a.ApproverNum))
                                        {
                                            <tr>
                                                <td>
                                                    <span class="badge bg-primary">Level @approver.ApproverNum</span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-2">
                                                            @approver.Email.Substring(0, 1).ToUpper()
                                                        </div>
                                                        @approver.Email
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="status-indicator status-approved">Active</span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                                onclick="editApprover(@approver.Id, '@approver.Email', @approver.ApproverNum)"
                                                                title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <form method="post" asp-page-handler="Delete" class="d-inline">
                                                            <input type="hidden" name="id" value="@approver.Id" />
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                    title="Delete"
                                                                    onclick="return confirm('Are you sure you want to remove this approver?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                                <h6 class="text-muted">No approvers configured</h6>
                                <p class="text-muted mb-3">Add approvers to enable the approval workflow.</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addApproverModal">
                                    <i class="fas fa-plus me-2"></i>Add First Approver
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Approval Workflow</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Total Approval Levels</label>
                            <div class="h4 text-primary">@Model.Approvers.Count</div>
                        </div>
                        
                        @if (Model.Approvers.Any())
                        {
                            <div class="mb-3">
                                <label class="form-label fw-bold">Workflow Process</label>
                                <div class="approval-flow">
                                    @foreach (var approver in Model.Approvers.OrderBy(a => a.ApproverNum))
                                    {
                                        <div class="approval-step">
                                            <div class="step-number">@approver.ApproverNum</div>
                                            <div class="step-content">
                                                <div class="fw-bold">Level @approver.ApproverNum</div>
                                                <small class="text-muted">@approver.Email</small>
                                            </div>
                                        </div>
                                        @if (approver != Model.Approvers.OrderBy(a => a.ApproverNum).Last())
                                        {
                                            <div class="step-arrow">
                                                <i class="fas fa-arrow-down text-muted"></i>
                                            </div>
                                        }
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-1"></i>How It Works</h6>
                            <ul class="mb-0 small">
                                <li>Applications require approval from all levels</li>
                                <li>Approvers are notified via email</li>
                                <li>Each level must approve before moving to the next</li>
                                <li>Any level can reject the application</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-1"></i>Important</h6>
                            <p class="mb-0 small">Removing approvers will affect pending applications. Ensure all pending reviews are completed first.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Approver Modal -->
<div class="modal fade" id="addApproverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Approver</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" asp-page-handler="Add">
                <div class="modal-body">
                    @if (Model.AvailableUsers.Any())
                    {
                        <div class="mb-3">
                            <label for="UserId" class="form-label">Select User</label>
                            <select class="form-select" name="UserId" required>
                                <option value="">Choose a user...</option>
                                @foreach (var user in Model.AvailableUsers.Where(u => !Model.Approvers.Any(a => a.Email == u.Email)))
                                {
                                    <option value="@user.UserID">
                                        @user.Email (@user.Role)
                                    </option>
                                }
                            </select>
                            <div class="form-text">Select an Admin or Approver user to assign as an approver.</div>
                        </div>
                        <div class="mb-3">
                            <label for="ApproverNum" class="form-label">Approval Level</label>
                            <select class="form-select" name="ApproverNum" required>
                                @for (int i = 1; i <= (Model.Approvers.Count + 1); i++)
                                {
                                    <option value="@i">Level @i</option>
                                }
                            </select>
                            <div class="form-text">Choose the approval level for this approver.</div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No Admin or Approver users available. Please create Admin or Approver users first.
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    @if (Model.AvailableUsers.Any(u => !Model.Approvers.Any(a => a.Email == u.Email)))
                    {
                        <button type="submit" class="btn btn-primary">Add Approver</button>
                    }
                    else
                    {
                        <a asp-page="/Admin/Users/<USER>" class="btn btn-primary">Create User First</a>
                    }
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Approver Modal -->
<div class="modal fade" id="editApproverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Approver</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" asp-page-handler="Edit">
                <input type="hidden" name="Id" id="editId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email Address</label>
                        <input type="email" class="form-control" name="Email" id="editEmail" required>
                    </div>
                    <div class="mb-3">
                        <label for="editApproverNum" class="form-label">Approval Level</label>
                        <select class="form-select" name="ApproverNum" id="editApproverNum" required>
                            @for (int i = 1; i <= Math.Max(Model.Approvers.Count, 1); i++)
                            {
                                <option value="@i">Level @i</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Approver</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function editApprover(id, email, approverNum) {
            document.getElementById('editId').value = id;
            document.getElementById('editEmail').value = email;
            document.getElementById('editApproverNum').value = approverNum;
            
            var editModal = new bootstrap.Modal(document.getElementById('editApproverModal'));
            editModal.show();
        }
    </script>
}
